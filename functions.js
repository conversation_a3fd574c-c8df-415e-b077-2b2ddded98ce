// INNER AND OUTER SLIDER
function addAnimation()
{
  $('.outerSlider-slick .default-col').removeClass('addAnimation');
  $('.outerSlider-slick .default-col').addClass('addAnimation'); 
}
function filterCarModel(getCarModel)
{
  $('.outerSlider-slick').slick('slickUnfilter');
  $('.outerSlider-slick').slick('slickFilter','[data-tab="' + getCarModel + '"]');
}
function sliderTogle()
{
  $('.outerSliderContainer-slick').show();
  $('.innerSliderContainer-slick').hide();
}
function filterTogle()
{
  $('.innerSlider-slick').slick('slickUnfilter');
  $('.outerSlider-slick').slick('slickUnfilter');
}
function resetNumberTagging()
{
  $( ".outerSlider-slick .default-col" ).each(function(index) {
    $(this).find( ".moreFeatureBtn" ).attr('data-inner', index);

    getTabIndex = $(this).find( ".moreFeatureBtn" ).attr('data-inner');
    console.log(getTabIndex);
  });
}
function toggleArrow()
{
  $('#outerSlider-arrow').removeClass('displayNone');
  $('#outerSlider-arrow').addClass('displayBlock');

  $('#innerSlider-arrow').addClass('displayNone');
  $('#innerSlider-arrow').removeClass('displayBlock');
}
function reAppendOuterSlider()
{
    $('.outerSlider-slick').slick('unslick');
    resetNumberTagging();
    
  	$('.outerSlider-slick').slick({
	    variableWidth: true,
	    slidesToShow: 1,
	    slidesToScroll: 1,
      infinite: false,
	    prevArrow: $('#outerSlider-previous'),
	    nextArrow: $('#outerSlider-next')
	});
}
function reAppendInnerSlider(sliderLayout)
{
  $('.innerSliderContainer-slick').empty();
  $.ajax({
      type: "GET",
      url: "slider/innerSlider.php", 
      success: function(response){
        var appendNew = '<div class="innerSlider-slick">';
        appendNew += response;
        appendNew += '</div>';
        $('.innerSliderContainer-slick').append(appendNew);

        $('.innerSlider-slick').slick(
        {
          slidesToShow: 1,
          slidesToScroll: 1,
          initialSlide: parseInt(getDataInner),
          prevArrow: $('#innerSlider-previous'),
          nextArrow: $('#innerSlider-next')
        });
        if(sliderLayout == true)
        {
          if(getDataTab == "sedan")
          {
            $('.innerSlider-slick').slick('slickUnfilter');
            $('.innerSlider-slick').slick('slickFilter', "[data-tab='sedan']");
          }
          else if(getDataTab == "suv")
          {
            $('.innerSlider-slick').slick('slickUnfilter');
            $('.innerSlider-slick').slick('slickFilter', "[data-tab='suv']");
          }
          else if(getDataTab == "hatchback")
          {
            $('.innerSlider-slick').slick('slickUnfilter');
            $('.innerSlider-slick').slick('slickFilter', "[data-tab='hatchback']");
          }
        }
        else
        {
          console.log('no filter');
        }

        if( $('#innerSlider-previous').hasClass('slick-hidden') )
        {
          $('#innerSlider-arrow div:nth-child(1)').hide();
        }
        else
        {
          $('#innerSlider-arrow div:nth-child(1)').show();
        } 

        $('.innerSlider-slick .default-col').removeClass('addFadeAnimation');
        $('.innerSlider-slick .default-col').addClass('addFadeAnimation'); 


        $('.click360Modal').click(function()
        {
            get360Link = $(this).attr('data-src');
            

            $('#insertFullImgLink').empty();
            $('#insertFullImgLink').append('<iframe allowfullscreen style="border-style:none;" src="'+get360Link+'"style="width: 100%;" height="610"></iframe>');
        });

        $('.clickYoutube').click(function()
        {
            youtubeLink = $(this).attr('data-youtube');
            

            $('#insertYoutubeLink').empty();
            $('#insertYoutubeLink').append('<iframe allowfullscreen style="border-style:none;" src="'+youtubeLink+'"style="width: 100%;" height="400"></iframe>');
        });

        $('.setCarModel').click(function()
        {
          getCarModel = $(this).attr('data-car-model');
          console.log(getCarModel);

          $('#modalInterest-bookADrive').val(getCarModel);

          $('#modalInterest-bookADrive').siblings('.anchor').addClass('hide');
          $('#modalInterest-bookADrive').siblings('.placehoder').addClass('focus');
          $('#modalInterest-bookADrive').siblings('.tick').addClass('show');
          $('#modalInterest-bookADrive').addClass('confirm');
        });
      }
  });
}


// FORM
function isNumberKey(evt){
    var charCode = (evt.which) ? evt.which : evt.keyCode
    return !(charCode > 31 && (charCode < 48 || charCode > 57));
}
function showErrorMessageBAD()
{
    $('#errorMessage-bookADrive').addClass('show');
    $('#errorMessage-bookADrive').text('Please fill in the required fields.');
}
function showSucessMessageBAD()
{
    $('#sucessMessage-bookADrive').addClass('show')
}
function showErrorMessageSU()
{
    $('#errorMessage-signUp').addClass('show');
    $('#errorMessage-signUp').text('Please fill in the required fields.');
}
function showSucessMessageSU()
{
    $('#sucessMessage-signUp').addClass('show')
}
function setSlidesToShow()
{
  var width = $( window ).width();

  if(width > 1640)
  {
    return 4;
  }
  else if(width >= 1440 && width <= 1640)
  {
    return 4;
  }
  else if(width > 1024 && width < 1440)
  {
    return 3;
  }
  else
  {
    return 1;
  }
}