$(document).ready(function () {
  $("#showBookForm").click(function () {
    $(".defaultForm").addClass("showForm");

    $("html").addClass("addOverflow");
    $("body").addClass("addOverflow");
  });
  $(".backToHomeBtn").click(function () {
    $(".defaultForm").removeClass("showForm");

    $("html").removeClass("addOverflow");
    $("body").removeClass("addOverflow");
  });

  // Toggle active class on filter tab (Browse our range of models)
  $(".browseCarModel-tabLeft div").click(function () {
    $(".browseCarModel-tabLeft div").removeClass("active");
    $(this).addClass("active");
  });

  //Toggle car model overlay
  $(".carText")
    .mouseenter(function () {
      $(this)
        .siblings(".overlay-grey")
        .css("background", "rgba(178, 183, 188, 0)");
      $(this)
        .siblings(".overlay-blue")
        .css("background", "rgba(17, 95, 179, 0)");
      $(this).siblings(".overlay-black").css("background", "rgba(0, 0, 0, .0)");
      $(this)
        .siblings(".overlay-white")
        .css("background", "rgba(255, 255, 255, 0)");
      $(this)
        .siblings(".overlay-darkgrey")
        .css("background", "rgba(31, 32, 36, 0)");
      $(this).siblings(".outerSlider-box").css("filter", "grayscale(0)");
    })
    .mouseleave(function () {
      $(this)
        .siblings(".overlay-grey")
        .css("background", "rgba(178, 183, 188, .5)");
      $(this)
        .siblings(".overlay-blue")
        .css("background", "rgba(17, 95, 179, .5)");
      $(this).siblings(".overlay-black").css("background", "rgba(0, 0, 0, .5)");
      $(this)
        .siblings(".overlay-white")
        .css("background", "rgba(255, 255, 255, .5)");
      $(this)
        .siblings(".overlay-darkgrey")
        .css("background", "rgba(31, 32, 36, .5)");
      $(this).siblings(".outerSlider-box").css("filter", "grayscale(100%)");
    });

  // Mobile slick for
  $(".mobile-slick").slick({
    variableWidth: true,
    slidesToShow: 1,
    slidesToScroll: 1,
    arrows: false,
    infinite: false,
  });

  //SIMPLE ANIMATION
  //HEADER
  var action = gsap
    .timeline()
    .from(".logoBar", {
      duration: 0.3,
      y: "-30px",
      opacity: "0",
      stagger: 0.3,
    })
    .from(".animateHeader1 a", {
      duration: 0.3,
      y: "-30px",
      opacity: "0",
      stagger: 0.3,
    })
    .from(".animateHeader2", { duration: 0.3, y: "-30px", opacity: "0" });

  var controller = new ScrollMagic.Controller();
  var scene = new ScrollMagic.Scene({
    triggerElement: ".animateHeader1",
    triggerHook: 0.3,
    reverse: false,
  })
    .setTween(action)
    .addTo(controller);

  //BANNER
  var action = gsap
    .timeline()
    .from(".animateHeader3", { duration: 0.3, x: "-30px", opacity: "0" })
    .from(".animateHeader4  button", {
      duration: 0.3,
      x: "-30px",
      opacity: "0",
    })
    .from(".animateHeader5", { duration: 0.3, x: "-30px", opacity: "0" });

  var controller = new ScrollMagic.Controller();
  var scene = new ScrollMagic.Scene({
    triggerElement: ".bannerSection",
    triggerHook: 0.6,
    reverse: false,
  })
    .setTween(action)
    .addTo(controller);

  //CAR MODEL
  var action = gsap.timeline();
  action.from(".animateHeader6", { duration: 0.3, x: "-30px", opacity: "0" });
  action.from(".browseCarModel-tabLeft div", {
    duration: 0.3,
    x: "-30px",
    opacity: "0",
    stagger: 00,
  });
  if ($(window).width() < 768)
    action.from(".outerSlider-slick", {
      duration: 0.3,
      x: "-30px",
      opacity: "0",
      stagger: 0.3,
    });
  else {
    action.from(".animateModelNavi", {
      duration: 0.3,
      x: "-30px",
      opacity: "0",
    });
    action.from(".outerSlider-slick", {
      duration: 0.3,
      x: "-30px",
      opacity: "0",
      stagger: 0.3,
    });
  }

  var controller = new ScrollMagic.Controller();
  var scene = new ScrollMagic.Scene({
    triggerElement: ".animateHeader6",
    triggerHook: 0.9,
    reverse: false,
  })
    .setTween(action)
    .addTo(controller);

  //FEATURE 1
  var action = gsap
    .timeline()
    .from(".animateHeader11", { duration: 0.3, opacity: "0" })
    .from(".animateHeader10 div", {
      duration: 0.3,
      x: "-30px",
      opacity: "0",
      stagger: 0.3,
    })
    .from(".animateHeader10 button", {
      duration: 0.3,
      x: "-30px",
      opacity: "0",
    });

  var controller = new ScrollMagic.Controller();
  var scene = new ScrollMagic.Scene({
    triggerElement: ".animateHeader10",
    triggerHook: 0.9,
    reverse: false,
  })
    .setTween(action)
    .addTo(controller);

  //FEATURE 2
  var action = gsap
    .timeline()
    .from(".animateHeader12", { duration: 0.3, opacity: "0" })
    .from(".animateHeader13 div", {
      duration: 0.3,
      x: "-30px",
      opacity: "0",
      stagger: 0.3,
    })
    .from(".animateHeader13 button", {
      duration: 0.3,
      x: "-30px",
      opacity: "0",
    });

  var controller = new ScrollMagic.Controller();
  var scene = new ScrollMagic.Scene({
    triggerElement: ".animateHeader12",
    triggerHook: 0.9,
    reverse: false,
  })
    .setTween(action)
    .addTo(controller);

  //FEATURE 3
  var action = gsap
    .timeline()
    .from(".grid-container .item1", { duration: 0.3, opacity: "0" })
    .from(".grid-container .item2", { duration: 0.3, opacity: "0" });

  var controller = new ScrollMagic.Controller();
  var scene = new ScrollMagic.Scene({
    triggerElement: ".grid-container .item1",
    triggerHook: 0.9,
    reverse: false,
  })
    .setTween(action)
    .addTo(controller);

  //FEATURE 4
  var action = gsap.timeline();
  action.from(".animateHeader14 div", {
    duration: 0.3,
    opacity: "0",
    stagger: 0.3,
  });

  if ($(window).width() < 768)
    action.from(".mobile-slick a", {
      duration: 0.3,
      opacity: "0",
      x: "-30px",
      stagger: 0.3,
    });
  else
    action.from(".grid-container1 a", {
      duration: 0.3,
      opacity: "0",
      y: "-30px",
      stagger: 0.3,
    });

  var controller = new ScrollMagic.Controller();
  var scene = new ScrollMagic.Scene({
    triggerElement: "#you-might-interested-anhor",
    triggerHook: 0.9,
    reverse: false,
  })
    .setTween(action)
    .addTo(controller);

  //FOOTER
  var action = gsap.timeline();
  action.from(".animateHeader15", { duration: 0.3, opacity: "0" });
  action.from(".socialIcon a", { duration: 0.3, opacity: "0", stagger: 0.3 });
  if ($(window).width() < 768) {
    action.from(".animateHeader17", { duration: 0.3, opacity: "0" });
    action.from(".animateHeader16-a", {
      duration: 0.3,
      opacity: "0",
      stagger: 0.3,
    });
    action.from(".animateHeader16-b", {
      duration: 0.3,
      opacity: "0",
      stagger: 0.3,
    });
    action.from(".animateHeader16-c", {
      duration: 0.3,
      opacity: "0",
      stagger: 0.3,
    });
  } else {
    action.from(".animateHeader16-a", {
      duration: 0.3,
      opacity: "0",
      stagger: 0.3,
    });
    action.from(".animateHeader16-b", {
      duration: 0.3,
      opacity: "0",
      stagger: 0.3,
    });
    action.from(".animateHeader16-c", {
      duration: 0.3,
      opacity: "0",
      stagger: 0.3,
    });
    action.from(".animateHeader17", { duration: 0.3, opacity: "0" });
  }

  var controller = new ScrollMagic.Controller();
  var scene = new ScrollMagic.Scene({
    triggerElement: ".animateHeader15-target",
    triggerHook: 0.9,
    reverse: false,
  })
    .setTween(action)
    .addTo(controller);

  var car_model_swiper = new Swiper(".discover-model-wrapper .mySwiper", {
    slidesPerView: 1,
    spaceBetween: 30,
    loop: true,
    navigation: {
      nextEl: '.discover-model-arrow.right',
      prevEl: '.discover-model-arrow.left',
    },
    breakpoints: {
      // when window width is >= 320px
      1025: {
        slidesPerView: 3,
      },
    }
  });
  var windowWidth = $(window).width();
  if( windowWidth <= 540 )
  {
    $('#collapseExample').removeClass('show');
  }
  $('.hamburger-menu-line').click(function()
  {
    $('#mySidenav').toggleClass('show');
  });
  $('#mySidenav a').click(function()
  {
    console.log('test');
    $('#mySidenav').toggleClass('show');
  });
  AOS.init();

  $('.set-modal-interest-input').click(function()
  {
    getCarModel = $(this).attr('data-car-model');
    console.log(getCarModel);

    $('#modalInterest-bookADrive').val(getCarModel);

    $('#modalInterest-bookADrive').siblings('.anchor').addClass('hide');
    $('#modalInterest-bookADrive').siblings('.tick').addClass('show');
    $('#modalInterest-bookADrive').addClass('confirm');
  });
});

// Show or hide the button based on scroll position
window.onscroll = function() {
    showHideReturnButton();
};
function showHideReturnButton() {
    var button = document.getElementById('return-to-top');
    if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
        button.style.display = 'block';
    } else {
        button.style.display = 'none';
    }
}
// Scroll to the top when the button is clicked
function returnToTop() {
    document.body.scrollTop = 0;
    document.documentElement.scrollTop = 0;
}
