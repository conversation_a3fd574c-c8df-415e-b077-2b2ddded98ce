
$(document).ready(function () {
  $("input").focus(function () {
    $(this).siblings(".placehoder").addClass("focus");

    $(this).removeClass("error");
    $(this).siblings(".placehoder").removeClass("error");
    $(this).siblings(".anchor").find("img").removeClass("error");
    $(this).siblings(".error-alert-icon").removeClass("show");
    $(this).closest('.input-wrapper').siblings(".error-message ").addClass('d-none');
  });
  $("input").focusout(function () {
    getVal = $(this).val();
    if (getVal) {
      $(this).siblings(".tick").addClass("show");
      $(this).addClass("confirm");
      $(this).siblings(".error-alert-icon").removeClass("show");
      $(this).closest('.input-wrapper').siblings(".error-message ").addClass('d-none');
    } else {
      $(this).removeClass("confirm");
      $(this).siblings(".placehoder").removeClass("focus");
      $(this).siblings(".tick").removeClass("show");

      $(this).removeClass("error");
      $(this).siblings(".placehoder").removeClass("error");
      $(this).siblings(".anchor").find("img").removeClass("error");
    }
  });

  var pattern = /^\b[A-Z0-9._%-]+@[A-Z0-9.-]+\.[A-Z]{2,4}\b$/i;
  $("input[name*='emai']").focusout(function () {
    getVal = $(this).val();

    if (getVal) {
      if (!pattern.test(getVal)) {
        $(this).siblings(".tick").removeClass("show");
        $(this).removeClass("confirm");
      } else {
        $(this).siblings(".tick").addClass("show");
        $(this).addClass("confirm");
        $(this).siblings(".error-alert-icon").removeClass("show");
        $(this).closest('.input-wrapper').siblings(".error-message ").addClass('d-none');
      }
    } else {
      $(this).removeClass("confirm");
      $(this).siblings(".placehoder").removeClass("focus");
      $(this).siblings(".tick").removeClass("show");
    }
  });

  // Malaysia Phone Validation
  // $("#contactNumber-bookADrive").focusout(function () {
  //   getVal = $(this).val();
  //   console.log(getVal.length);

  //   if (getVal) {
  //     if (getVal.length >= 10 && getVal.length <= 12) {
  //       $(this).siblings(".tick").addClass("show");
  //       $(this).addClass("confirm");
  //       $(this).siblings(".error-alert-icon").removeClass("show");
  //       $(this).closest('.input-wrapper').siblings(".error-message ").addClass('d-none');
  //     } else {
  //       $(this).siblings(".tick").removeClass("show");
  //       $(this).removeClass("confirm");
  //     }
  //   } else {
  //     $(this).removeClass("confirm");
  //     $(this).siblings(".placehoder").removeClass("focus");
  //     $(this).siblings(".tick").removeClass("show");
  //   }
  // });
  
 // Singapore Phone Validation
 $("#contactNumber-bookADrive").focusout(function () {
  var getVal = $(this).val().replace(/\s+/g, ''); // Remove any whitespace

  // Regular expression to validate Singapore phone number
  var regex = /^(?:\+65|65)?[689]\d{7}$/;

  if (getVal) {
      if (regex.test(getVal)) {
          $(this).siblings(".tick").addClass("show");
          $(this).addClass("confirm");
      } else {
          $(this).siblings(".tick").removeClass("show");
          $(this).removeClass("confirm");
      }
  } else {
      $(this).removeClass("confirm");
      $(this).siblings(".placehoder").removeClass("focus");
      $(this).siblings(".tick").removeClass("show");
  }
});
  // $("select").change(function () {
  //   $(this).siblings(".placehoder").addClass("focus");

  //   $(this).removeClass("error");
  //   $(this).siblings(".placehoder").removeClass("error");
  //   $(this).siblings(".anchor").find("img").removeClass("error");

  //   getVal = $(this).val();
  //   if (getVal) {
  //     $(this).siblings(".anchor").addClass("hide");
  //     $(this).siblings(".tick").addClass("show");
  //     $(this).addClass("confirm");
  //   } else {
  //     $(this).siblings(".placehoder").removeClass("focus");
  //     $(this).siblings(".tick").removeClass("show");
  //     $(this).siblings(".anchor").removeClass("hide");
  //   }
  // });

  $("select").change(function () {
    $(this).siblings(".placehoder").addClass("focus");

    $(this).removeClass("error");
    $(this).siblings(".placehoder").removeClass("error");
    $(this).siblings(".anchor").find("img").removeClass("error");

    getVal = $(this).val();
    if (getVal) {
        $(this).siblings(".anchor").addClass("hide");
        $(this).siblings(".tick").addClass("show");
        $(this).addClass("confirm");
        $(this).siblings(".error-alert-icon").removeClass("show");
        $(this).closest('.input-wrapper').siblings(".error-message ").addClass('d-none');
    } else {
        $(this).siblings(".placehoder").removeClass("focus");
        $(this).siblings(".tick").removeClass("show");
        $(this).siblings(".anchor").removeClass("hide");
    }
  });

  // BOOK A TEST DRIVE
  // Datepicker Start Date Add Date
  // var someDate = new Date();
  // someDate.setDate(someDate.getDate() + 1);

  // $("#preferableDate-bookADrive")
  //   .datepicker({
  //     autoclose: true,
  //     format: "dd M yyyy",
  //     startDate: someDate,
  //   })
  //   .on("change", function () {
  //     $("#preferableDate-bookADrive").siblings(".placehoder").addClass("focus");
  //     $("#preferableDate-bookADrive").siblings(".tick").addClass("show");
  //     $("#preferableDate-bookADrive").addClass("confirm");
  //   })
  //   .on("focus", function () {
  //     $(this).trigger("blur");
  //   });

  // BOOK A TEST DRIVE
  // Define the minimum allowed date (14 March 2025)
  var minAllowedDate = new Date(2025, 2, 15); // March is 2 (zero-based)
  var today = new Date();
  today.setHours(0, 0, 0, 0); // Reset time to compare only date

  // Determine the start date: Either 14 March 2025 or the next day after today
  var startDate = today > minAllowedDate ? new Date(today.setDate(today.getDate() + 1)) : minAllowedDate;

  $("#preferableDate-bookADrive")
    .datepicker({
      autoclose: true,
      format: "dd M yyyy",
      startDate: startDate,
    })
    .on("change", function () {
      $("#preferableDate-bookADrive").siblings(".placehoder").addClass("focus");
      $("#preferableDate-bookADrive").siblings(".tick").addClass("show");
      $("#preferableDate-bookADrive").addClass("confirm");
    })
    .on("focus", function () {
      $(this).trigger("blur");
    });

  

// Initialize timepicker
$("#preferableTime-bookADrive").timepicker({
  interval: 60, // Set time interval to 1 hour
  timeFormat: "h:i A", // Format for displaying time
  minTime: "10:00am", // Minimum allowed time
  maxTime: "6:00pm", // Maximum allowed time
  defaultTime: "10:00am", // Default time when the input field is empty
  scrollbar: true, // Show scrollbar for easier selection
});

// Additional condition for Outlook integration
$("#preferableTime-bookADrive").on("change", function () {
  var selectedTime = $(this).val();
  if (selectedTime === "Outlook") {
    // Handle Outlook integration logic here
    console.log("Outlook integration selected");
  }
});
  
//   // Timepicker
// $("#preferableTime-bookADrive").timepicker({
//   interval: 60, // 1 hour increments
//   minTime: '10:00am',
//   maxTime: '6:00pm',
//   timeFormat: 'h:i A',
// });

// // Condition for "Self booking" Outlook integration
// $("#preferableTime-bookADrive").on("change", function () {
//   if ($(this).val() === "Self booking") {
//     // Handle Outlook integration logic here
//     console.log("Self booking - Outlook integration");
//   }
// });

//  // Custom Timepicker
//  var timeOptions = generateTimeOptions("10:00am", "6:00pm", 60);
//  $("#preferableTime-bookADrive").html(timeOptions);

//  // Condition for "Self booking" Outlook integration
//  $("#preferableTime-bookADrive").on("change", function () {
//    if ($(this).val() === "Self booking") {
//      // Handle Outlook integration logic here
//      console.log("Self booking - Outlook integration");
//    }
//  });

//    // Function to generate time options
//    function generateTimeOptions(startTime, endTime, interval) {
//     var options = "<option value=''>Select Time</option>";
//     var currentTime = new Date("2023-01-01 " + startTime);

//     while (currentTime <= new Date("2023-01-01 " + endTime)) {
//       options += "<option value='" + formatTime(currentTime) + "'>" + formatTime(currentTime) + "</option>";
//       currentTime.setMinutes(currentTime.getMinutes() + interval);
//     }

//     return options;
//   }

//   // Function to format time
//   function formatTime(date) {
//     var hours = date.getHours();
//     var minutes = date.getMinutes();
//     var ampm = hours >= 12 ? "pm" : "am";
//     hours = hours % 12;
//     hours = hours ? hours : 12; // the hour '0' should be '12'
//     minutes = minutes < 10 ? "0" + minutes : minutes;
//     var strTime = hours + ":" + minutes + " " + ampm;
//     return strTime;
//   }


  // $("#selectedStated-bookADrive").change(function () {
  //   dataSelect = $("option:selected", this).attr("data-select");

    // $("#selectedDealer-bookADrive > option").each(function() {
    //     $("#selectedDealer-bookADrive").val('');
    //     $("#selectedDealer-bookADrive").siblings('.placehoder').removeClass('focus');
    //     $("#selectedDealer-bookADrive").siblings('.tick').removeClass('show');
    //     $("#selectedDealer-bookADrive").siblings('.anchor').removeClass('hide');

    //     dataSelectInner = $(this).attr('data-select');
    //     $(this).show();

    //     if(dataSelectInner != dataSelect)
    //     {
    //         $(this).hide();
    //     }
    // });

  //   $("#selectedDealer-bookADrive").empty();
  //   $.each(dealerArray, function (index) {
  //     if (dealerArray[index].state == dataSelect) {
  //       $("#selectedDealer-bookADrive").append(
  //         '<option selected style="display: none;"></option>'
  //       );
  //       $.each(dealerArray[index].dealer, function (index, data) {
  //         $("#selectedDealer-bookADrive").append(data.option);
  //       });
  //     }
  //   });

  //   $("#selectedDealer-bookADrive").removeClass("notactive");
  //   $("#selectedDealer-bookADrive").css("opacity", "1");
  //   $("#selectedDealer-bookADrive").removeClass("confirm");
  //   $("#selectedDealer-bookADrive")
  //     .siblings(".placehoder")
  //     .removeClass("focus");
  //   $("#selectedDealer-bookADrive").siblings(".tick").removeClass("show");
  // });
  var setRadioValueBTD = "Any",
    setPolicyBTD = "agree",
    setNewsletterBTD = "no";
  $(".radio-bookADrive").change(function () {
    setRadioValueBTD = $(this).val();
  });
  $("#sendRequest-bookTestDrive").click(function (e) {
    e.preventDefault();
    $("#errorMessage-bookADrive").removeClass("show");
    $("#errorMessage-bookADrive").show();
    var hidden_value = $("#hidden-value").val();

    // if (!$("#selectedStated-bookADrive").hasClass("confirm")) {
    //   $("#selectedStated-bookADrive").addClass("error");
    //   $("#selectedStated-bookADrive").siblings(".placehoder").addClass("error");
    //   $("#selectedStated-bookADrive")
    //     .siblings(".anchor")
    //     .find("img")
    //     .addClass("error");
    // }
    if (!$("#preferableDate-bookADrive").hasClass("confirm")) {
      $("#preferableDate-bookADrive").addClass("error");
      $("#preferableDate-bookADrive").siblings(".placehoder").addClass("error");
      $("#preferableDate-bookADrive")
        .siblings(".anchor")
        .find("img")
        .addClass("hide");
      $("#preferableDate-bookADrive")
        .siblings(".error-alert-icon")
        .addClass("show");
      $("#preferableDate-bookADrive").closest('.input-wrapper').siblings(".error-message ").removeClass('d-none');
    }
    if (!$("#preferableTime-bookADrive").hasClass("confirm")) {
      $("#preferableTime-bookADrive").addClass("error");
      $("#preferableTime-bookADrive").siblings(".placehoder").addClass("error");
      $("#preferableTime-bookADrive")
        .siblings(".anchor")
        .find("img")
        .addClass("hide");
      $("#preferableTime-bookADrive")
        .siblings(".error-alert-icon")
        .addClass("show");
      $("#preferableTime-bookADrive").closest('.input-wrapper').siblings(".error-message ").removeClass('d-none');
    }
    // if (!$("#selectedDealer-bookADrive").hasClass("confirm")) {
    //   $("#selectedDealer-bookADrive").addClass("error");
    //   $("#selectedDealer-bookADrive").siblings(".placehoder").addClass("error");
    //   $("#selectedDealer-bookADrive")
    //     .siblings(".anchor")
    //     .find("img")
    //     .addClass("error");
    // }
    if (!$("#modalInterest-bookADrive").hasClass("confirm")) {
      $("#modalInterest-bookADrive").addClass("error");
      $("#modalInterest-bookADrive").siblings(".placehoder").addClass("error");
      $("#modalInterest-bookADrive")
        .siblings(".anchor")
        .find("img")
        .addClass("hide");
      $("#modalInterest-bookADrive")
        .siblings(".error-alert-icon")
        .addClass("show");
      $("#modalInterest-bookADrive").closest('.input-wrapper').siblings(".error-message ").removeClass('d-none');
    }
    // if (!$("#additionalModalInterest-bookADrive").hasClass("confirm")) {
    //   $("#additionalModalInterest-bookADrive").addClass("error");
    //   $("#additionalModalInterest-bookADrive").siblings(".placehoder").addClass("error");
    //   $("#additionalModalInterest-bookADrive")
    //     .siblings(".anchor")
    //     .find("img")
    //     .addClass("hide");
    //   $("#additionalModalInterest-bookADrive")
    //     .siblings(".error-alert-icon")
    //     .addClass("show");
    //   $("#additionalModalInterest-bookADrive").closest('.input-wrapper').siblings(".error-message ").removeClass('d-none');
    // }
    if (!$("#fullName-bookADrive").hasClass("confirm")) {
      $("#fullName-bookADrive").addClass("error");
      $("#fullName-bookADrive").siblings(".placehoder").addClass("error");
      $("#fullName-bookADrive")
        .siblings(".anchor")
        .find("img")
        .addClass("hide");
      $("#fullName-bookADrive")
        .siblings(".error-alert-icon")
        .addClass("show");
      $("#fullName-bookADrive").closest('.input-wrapper').siblings(".error-message ").removeClass('d-none');
    }
    if (!$("#contactNumber-bookADrive").hasClass("confirm")) {
      $("#contactNumber-bookADrive").addClass("error");
      $("#contactNumber-bookADrive").siblings(".placehoder").addClass("error");
      $("#contactNumber-bookADrive")
        .siblings(".anchor")
        .find("img")
        .addClass("hide");
      $("#contactNumber-bookADrive")
        .siblings(".error-alert-icon")
        .addClass("show");
      $("#contactNumber-bookADrive").closest('.input-wrapper').siblings(".error-message ").removeClass('d-none');
    }
    if (!$("#email-bookADrive").hasClass("confirm")) {
      $("#email-bookADrive").addClass("error");
      $("#email-bookADrive").siblings(".placehoder").addClass("error");
      $("#email-bookADrive").siblings(".anchor").find("img").addClass("hide");
      $("#email-bookADrive").siblings(".error-alert-icon").addClass("show");
      $("#email-bookADrive").closest('.input-wrapper').siblings(".error-message ").removeClass('d-none');
    }


    // Function to generate time options with 1-hour increments from 10am to 6pm
  //   function generateTimeOptions() {
  //     var select = document.getElementById("timePicker");
  //     select.innerHTML = '';

  //     var hours = 10;
  //     var minutes = 0;

  //     while (hours < 18 || (hours === 18 && minutes === 0)) {
  //         var option = document.createElement("option");
  //         option.text = ('0' + hours).slice(-2) + ":" + ('0' + minutes).slice(-2);
  //         select.add(option);

  //         // Increment time by 1 hour
  //         if (minutes + 60 >= 60) {
  //             hours++;
  //             minutes = (minutes + 60) % 60;
  //         } else {
  //             minutes += 60;
  //         }
  //     }
  // }

  // // Call the function to generate time options
  // generateTimeOptions();

    preferableDate = $("#preferableDate-bookADrive").val();
    preferableTime = $("#preferableTime-bookADrive").val();
    // selectedDealer = $("#selectedDealer-bookADrive").val();
    modalInterest = $("#modalInterest-bookADrive").val();
    additionalModalInterest = $("#additionalModalInterest-bookADrive").val();
    fullName = $("#fullName-bookADrive").val();
    contactNumber = $("#contactNumber-bookADrive").val();
    email = $("#email-bookADrive").val();
    if (
      // $("#selectedStated-bookADrive").hasClass("confirm") &&
      $("#preferableDate-bookADrive").hasClass("confirm") &&
      $("#preferableTime-bookADrive").hasClass("confirm") &&
      // $("#selectedDealer-bookADrive").hasClass("confirm") &&
      $("#modalInterest-bookADrive").hasClass("confirm") &&
      $("#fullName-bookADrive").hasClass("confirm") &&
      $("#contactNumber-bookADrive").hasClass("confirm") &&
      $("#email-bookADrive").hasClass("confirm")
    ) {
      if ($("input#inlineCheckbox2-bookADrive").is(":checked")) {
        setNewsletterBTD = "yes";
      } else {
        setNewsletterBTD = "no";
      }

      if (!$("input#inlineCheckbox1-bookADrive").is(":checked")) {
        $("#errorMessage-bookADrive").addClass("show");
        $("#errorMessage-bookADrive").text("Please tick the Privacy Notice");
      } else {
        $("#errorMessage-bookADrive").removeClass("show");
        $("#errorMessage-bookADrive").hide();

        // CHECK DATA
        console.log("Book a test drive");
        console.log(
          // selectedStated +
            preferableDate +
            preferableTime +
            // selectedDealer +
            modalInterest +
            additionalModalInterest +
            fullName +
            contactNumber +
            email
        );
        console.log(setRadioValueBTD + setPolicyBTD + setNewsletterBTD);

        if (hidden_value == "") {
          // Privyr Webhook
          fetch("https://www.privyr.com/api/v1/incoming-leads/0vZfjMQw/tH9dDY4B", {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              name: fullName,
              phone: contactNumber,
              email: email,
              source: "Book a test drive",
              custom_fields: {
                "Preferred Date": preferableDate,
                "Preferred Time": preferableTime,
                "Model of Interest": modalInterest,
                "Additional Car": additionalModalInterest,
                "Contact via": setRadioValueBTD,
                "Newsletter": setNewsletterBTD
              }
            })
          })
          .then(response => {
            if (response.ok) {
              console.log("Privyr lead submitted");
            } else {
              console.error("Privyr error:", response.status);
            }
          })
          .catch(error => {
            console.error("Privyr network error:", error);
          });
          // Privyr Webhook end

          $.ajax({
            url: "https://hooks.zapier.com/hooks/catch/14727421/2a7zus4/",
            data: {
              // "Source of Lead": "website",
              // Form: "Book a test drive",
              // "Roadshow / Event Name": "",
              // "Appointment Made": preferableDate,
              // Remarks: "Preferable Time: " + preferableTime,
              // // "Preferable Dealer": selectedDealer,
              // "Model of Interest": modalInterest,
              // "Customer's Full Name": fullName,
              // "Contact Number": contactNumber,
              // "Email Address": email,
              // "Contact Via": setRadioValueBTD,
              // "Privacy Policy": setPolicyBTD,
              // Newsletter: setNewsletterBTD,
              // "Secret Key": "BATD-CUPRA-LandingPage",

              Source: "website",
              Form: "Book a test drive",
              Campaign: "",
              Date: preferableDate,
              Remarks: "Additionl Car: " + additionalModalInterest,
              Time: preferableTime,
              // "Preferable Dealer": selectedDealer,
              "Model of Interest": modalInterest,
              Name: fullName,
              Phone: contactNumber,
              Email: email,
              "Contact Via": setRadioValueBTD,
              "Privacy Policy": setPolicyBTD,
              Newsletter: setNewsletterBTD,
              "Secret Key": "BATD-CUPRA-LandingPage",
            },
            type: "POST",
            beforeSend: function () {
              $(".loader").addClass("show");
              $("#sendRequest-bookTestDrive").text("Sending");

              $("#sendRequest-bookTestDrive").addClass("notactive");
              $("#book-a-test-drive input").addClass("notactive");
              $("#book-a-test-drive select").addClass("notactive");

              $("#book-a-test-drive input").css("opacity", ".3");
              $("#book-a-test-drive select").css("opacity", ".3");
            },
            success: function (result) {
              csrf = $("#input-csrf").val();
              console.log(csrf);

              $.ajax({
                url: "NODNISMemail.php",
                type: "POST",
                data: {
               
              Source: "website",
              Form: "Book a test drive",
              Campaign: "",
              Date: preferableDate,
              Remarks: "Additionl Car: " + additionalModalInterest,
              Time: preferableTime,
              // "Preferable Dealer": selectedDealer,
              ModelInterest: modalInterest,
              Name: fullName,
              Phone: contactNumber,
              Email: email,
              "Contact Via": setRadioValueBTD,
              "Privacy Policy": setPolicyBTD,
              Newsletter: setNewsletterBTD,
                  Newsletter: setNewsletterBTD,
                  csrf: csrf,
                },
                success: function (t) {
                  $.redirect("thank-you.php");
                },
              });
            },
          });
        }
      }
    } else {
      showErrorMessageBAD();
    }
  });

  // SIGN UP FORM
  var setRadioValueSU = "Any",
    setPolicySU = "agree";
  $(".radio-signUp").change(function () {
    setRadioValueSU = $(this).val();

    $(".setError").removeClass("error");
  });
  $("#sendRequest-signUp").click(function (e) {
    e.preventDefault();
    $("#errorMessage-signUp").removeClass("show");
    $("#errorMessage-signUp").show();

    if (setRadioValueSU == "" || setRadioValueSU == "Any") {
      showErrorMessageSU();

      $(".setError").addClass("error");
    } else if (!$("#fullName-signUp").hasClass("confirm")) {
      showErrorMessageSU();

      $("#fullName-signUp").addClass("error");
      $("#fullName-signUp").siblings(".placehoder").addClass("error");
      $("#fullName-signUp").siblings(".anchor").find("img").addClass("error");
    } else if (!$("#email-signUp").hasClass("confirm")) {
      showErrorMessageSU();

      $("#email-signUp").addClass("error");
      $("#email-signUp").siblings(".placehoder").addClass("error");
      $("#email-signUp").siblings(".anchor").find("img").addClass("error");
    } else {
      title = $("#title-signUp").val();
      fullName = $("#fullName-signUp").val();
      email = $("#email-signUp").val();

      if (!$("input#inlineCheckbox1-signUp").is(":checked")) {
        $("#errorMessage-signUp").addClass("show");
        $("#errorMessage-signUp").text("Please tick the Privacy Notice");
      } else {
        $("#errorMessage-signUp").removeClass("show");
        $("#errorMessage-signUp").hide();

        // CHECK DATA
        console.log("Sign Up");
        console.log(title + fullName + email);
        console.log(setRadioValueSU + setPolicySU);

        $.ajax({
          url: "",
          data: {
            Website: "Book a test drive",
            Form: "Sign Up",
            Campaign: "Facebook",
            Salutation: setRadioValueSU,
            Title: title,
            Name: fullName,
            Email: email,
            "Privacy Policy": setPolicySU,
            "Secret Key": "O5TmVNTd4^q5",
          },
          beforeSend: function () {
            $(".loader").addClass("show");
            $("#sendRequest-signUp").text("Subscribing");

            $("#sendRequest-signUp").addClass("notactive");
            $("#sign-up-form input").addClass("notactive");
            $("#sign-up-form select").addClass("notactive");

            $("#sign-up-form input").css("opacity", ".3");
            $("#sign-up-form select").css("opacity", ".3");
          },
          type: "POST",
          success: function (result) {
            $.redirect("thank-you.php");
          },
        });
      }
    }
  });

    // LINK FORM
    var getUrlParameter = function getUrlParameter(sParam) {
      var sPageURL = window.location.search.substring(1),
          sURLVariables = sPageURL.split('&'),
          sParameterName,
          i;
  
      for (i = 0; i < sURLVariables.length; i++) {
          sParameterName = sURLVariables[i].split('=');
  
          if (sParameterName[0] === sParam) {
              return sParameterName[1] === undefined ? true : decodeURIComponent(sParameterName[1]);
          }
      }
      return false;
  
  };
  var modelInterest = getUrlParameter('modelInterest');
  
  if( modelInterest != ' ' || modelInterest != '' ){
    if( modelInterest == 'The Golf' || modelInterest == 'The Golf GTI' || modelInterest == 'The Golf R' || modelInterest == 'The Arteon' || modelInterest == 'The T-Cross' || modelInterest == 'The Tiguan' || modelInterest == 'The Touareg' || modelInterest == 'The Caddy 5' ){
      $('.bookTestDriveBtn').click();
      $("#modalInterest-bookADrive option[value='"+modelInterest+"']").attr('selected', 'selected');
      setTimeout(function(){
        $('#modalInterest-bookADrive').next().addClass('focus');
      }, 250);
    }
  }
});


