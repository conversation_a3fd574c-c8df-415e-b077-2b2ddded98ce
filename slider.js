$( document ).ready(function() {
    $('.carousel').carousel({
      interval: false,
    });


    $('.outerSlider-slick').slick({
      variableWidth: true,
      slidesToShow: setSlidesToShow(),
      slidesToScroll: 1,
      infinite: false,
      prevArrow: $('#outerSlider-previous'),
      nextArrow: $('#outerSlider-next')
    });
    $('.innerSliderContainer-slick').hide();
    if( $('#outerSlider-previous').hasClass('slick-hidden') )
    {
      $('#outerSlider-arrow div:nth-child(1)').hide();
    }
    else
    {
      $('#outerSlider-arrow div:nth-child(1)').show();
    } 

    var sliderLayout = false;
    $('.moreFeatureBtn').click(function()
    {
      $('.outerSliderContainer-slick').hide();
      $('.innerSliderContainer-slick').show();

      getDataInner = $(this).attr('data-inner');
      getDataTab = $(this).attr('data-tab');

      reAppendInnerSlider(sliderLayout);

      $('#outerSlider-arrow').addClass('displayNone');
      $('#outerSlider-arrow').removeClass('displayBlock');

      $('#innerSlider-arrow').removeClass('displayNone');
      $('#innerSlider-arrow').addClass('displayBlock');

      $('.browse-car-model').addClass('removePaddingBottom30');
    });

    $('#clickAllSedan, #clickAllSuv, #clickHatchback, #clickCommercialVehicles').click(function()
    {
      $('.browse-car-model').removeClass('removePaddingBottom30');
      sliderLayout = true;
      getCarModel = $(this).attr('data-tab');

      if( $( window ).width() < 768)
      {
        $('.outerSlider-slick').slick('slickUnfilter');
        $('.outerSlider-slick').slick('unslick');

        $('.outerSlider-slick').slick({
          variableWidth: true,
          slidesToShow: 1,
          slidesToScroll: 1,
          infinite: false,
          prevArrow: $('#outerSlider-previous'),
          nextArrow: $('#outerSlider-next')
        });

        $('.outerSlider-slick').slick('slickFilter','[data-tab="' + getCarModel + '"]');
      }
      else
        filterCarModel(getCarModel);

      resetNumberTagging();

      toggleArrow();
      addAnimation();
      sliderTogle();

      if( $('#outerSlider-previous').hasClass('slick-hidden') )
      {
        $('#outerSlider-arrow div:nth-child(1)').hide();
      }
      else
      {
        $('#outerSlider-arrow div:nth-child(1)').show();
      } 
    });
    $('#clickAll').click(function()
    {
      $('.browse-car-model').removeClass('removePaddingBottom30');
      sliderLayout = false;

      sliderTogle();
      filterTogle();
      reAppendOuterSlider();

      toggleArrow();
      addAnimation();

      if( $('#outerSlider-previous').hasClass('slick-hidden') )
      {
        $('#outerSlider-arrow div:nth-child(1)').hide();
      }
      else
      {
        $('#outerSlider-arrow div:nth-child(1)').show();
      } 
    });


    $('#closeInnerSlider').click(function()
    {
      if( $('#clickAll').hasClass('active') )
      {
        $('#clickAll').trigger('click');
      }
      else if( $('#clickAllSedan').hasClass('active') )
      {
        $('#clickAllSedan').trigger('click');
      }
      else if( $('#clickAllSuv').hasClass('active') )
      {
        $('#clickAllSuv').trigger('click');
      }
      else if( $('#clickCommercialVehicles').hasClass('active') )
      {
        $('#clickCommercialVehicles').trigger('click');
      }
      else
      {
        $('#clickHatchback').trigger('click');
      }
    });
});