<?php require 'CSRF_Protect.php';
$csrf = new CSRF_Protect();
 ?>

<!-- Store Data In Array Format -->
<?php include 'skoda-data.php' ?>
 
<!DOCTYPE html>
<html lang="en">
<head>
<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-K6VCKZH6');</script>
<!-- End Google Tag Manager -->

<title>Book a Test Drive | CUPRA Singapore</title>
<meta property="og:title" content="Book a Test Drive | Test Drive Request | CUPRA Singapore" />
<meta name="description" content="Experience CUPRA for yourself with a test drive today.​" />
<meta property="og:description" content="Experience CUPRA for yourself with a test drive today." />
<meta property="og:site_name" content="CUPRA Singapore" />
<meta property="og:image" content="https://testdrive.cupraofficial.sg//img/og-image-1.jpg" />
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">

  <?php include 'head.php' ?>


</head>
<body>
<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-K6VCKZH6"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->
  <!-- HEADER -->
  <?php include 'header.php' ?>

  <!-- BANNER -->
  <section class="bannerSection" id="skodaBannerSection">

<!--     <img src="img/banner-batd-desktop.jpg" style="width: 100%;" class="d-md-block d-none">
    <img src="img/banner-batd-mobile.jpg" style="width: 100%;" class="d-md-none d-block"> -->

    <div class="bannerPosition justify-content-xl-start justify-content-center align-items-end h-100">
      <div class="normal-container mb-xl-5 pb-5">
        <div class="font60 row justify-content-xl-start justify-content-center" data-aos="fade-left" data-aos-once="true" data-aos-duration="800">
          <div class="col-lg-8 col-10 text-xl-left text-center">
            <div class="cupra-book main-title font60" style="line-height:78px;">EXCITE YOUR SENSES.<br >REQUEST A TEST DRIVE.</div>
          </div>
        </div>
        <!-- <div class="d-flex align-items-center justify-content-xl-start justify-content-center mt-3 pt-lg-1" style="cursor: pointer;" data-aos="fade-left" data-aos-once="true" data-aos-duration="800" data-aos-delay="200">
          <button class="bookTestDriveBtn cupra-book whiteHover font16" data-toggle="modal" data-target="#book-a-test-drive">Book a Test Drive</button>
        </div> -->
      </div>
    </div>
  </section>

  <!-- DISCOVER THE SKODA RANGE -->
<!-- DISCOVER THE SKODA RANGE -->
<!-- DISCOVER THE SKODA RANGE -->
<section class="discover-model-section py-5 overflow-hidden" id="browse-car-model-anchor">
    <div class="normal-container position-relative">
        <p class="m-0 p-0 text-center cupra-book font60" data-aos="fade-left" data-aos-once="true" data-aos-duration="800">REBELS OF THE ROAD</p>
        <div class="discover-model-wrapper px-lg-5" data-aos="fade-right" data-aos-once="true" data-aos-duration="800" style="margin-top: 5% !important;">
            <div class="swiper mySwiper">
                <div class="swiper-wrapper">
                    <?php foreach ($car_model as $model): ?>
                        <div class="discover-model-items swiper-slide" style="cursor:pointer;">
                            <p class="m-0 p-0 modal-name text-center cupra-book mx-auto" style="margin-top:-10px !important; margin-bottom:5% !important">
                                <?php echo $model['name']; ?>
                            </p>
                            <img src="<?php echo $model['img']; ?>" alt="<?php echo $model['name']; ?>" class="w-100 h-auto position-relative mx-auto modal-image">

                            <div class="d-flex justify-content-center align-items-center mt-2 pt-1 <?php if (strpos($model['name'], 'Born') === false) echo 'apply-padding-top'; ?>">
                                <a href="<?php echo $model['explore_url']; ?>" target="_blank">
                                    <div class="btn2 cupra-book exploreBtn text-capitalize mr-3">Explore</div>
                                </a>
                                <div class="btn2 cupra-book set-modal-interest-input" data-toggle="modal" data-target="#book-a-test-drive" data-car-model="<?php echo $model['name']; ?>">Book a Test Drive</div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                <div class="swiper-pagination d-md-none"></div>
            </div>
        </div>

        <div class="d-flex justify-content-center align-items-center w-100 mt-md-0 mt-4 d-none d-md-flex">
            <div class="discover-model-arrow position-absolute left text-white px-3 py-2 mr-md-0 mr-3">
                <i class="fa fa-angle-left"></i>
            </div>
            <div class="discover-model-arrow position-absolute right text-white px-3 py-2">
                <i class="fa fa-angle-right"></i>
            </div>
        </div>
    </div>
</section>

<style>
@media (max-width: 768px) {
    /* .swiper .swiper-wrapper {
        display: flex;
        flex-direction: row;
    }
    .swiper-slide {
        width: 100% !important;
        text-align: left !important;
    /* } */
    /* .swiper-pagination {
        position: relative;
        bottom: -20px;
        text-align: center;
        display: block !important;
        padding-top: 20px;
    }
    .swiper-pagination .swiper-pagination-bullet {
        background-color: black;
        width: 20px;
        height: 5px;
        margin: 5px;
        border-radius: 2px;
    }
    .swiper-pagination .swiper-pagination-bullet-active {
        background-color: #333;
    }
    .discover-model-items {
        margin-left: -2% !important;
    }
    .discover-model-arrow {
        display: none !important;
    }
    .swiper-pagination .swiper-pagination-bullet-active {
        background-color: #333;
        width: 40px;
    } */
}
@media (min-width: 769px) {
    .swiper-slide {
        text-align: center !important;
    }
    /* .swiper-wrapper{
        display: flex  !important;
        justify-content: center !important;
    } */
    /* .swiper-pagination {
        display: none !important;
    } */
    /* .discover-model-arrow{
      display: none !important;
    } */
}
</style>

<!-- <script>
document.addEventListener("DOMContentLoaded", function() {
    var swiper = new Swiper(".mySwiper", {
        loop: true,
        pagination: {
            el: ".swiper-pagination",
            clickable: true,
        },
        navigation: {
            nextEl: ".discover-model-arrow.right",
            prevEl: ".discover-model-arrow.left",
        },
        breakpoints: {
            768: {
                navigation: false,
            }
        }
    });
});
</script> -->


<!-- <script>
var swiper = new Swiper(".swiper", {
    slidesPerView: 1,
    spaceBetween: 0,
    loop: true,
    pagination: {
        el: ".swiper-pagination",
        clickable: true,
        dynamicBullets: true,
    },
    navigation: {
        nextEl: ".discover-model-arrow.right",
        prevEl: ".discover-model-arrow.left",
    },
    breakpoints: {
        768: {
            slidesPerView: 'auto',
            navigation: false,
        }
    }
});
</script> -->



    <div class="slider-container1"  id="you-might-interested-anchor"> 
    <p class="m-0 p-0 text-center cupra-book font40" data-aos="fade-left" data-aos-once="true" data-aos-duration="800" style="color: white">EXPLORE CUPRA</p>
        <button class="slider-btn1 prev1" onclick="moveSlide1(-2)">&#10094;</button>
        <div class="slider-wrapper1">
            <div class="slide1">
                <div class="card1">
                    <img src="img/cupra/cupra-newsletter.jpg" alt="Newsletter">
                    <div class="card-content1">
                    <span class="m-0 p-0 cupra-regular font20">Newsletter</span>
                    <div class="cupra-light">
                        <p>Sign up and always stay up to date!</p>
                        </div>
                        <a href="https://www.cupraofficial.sg/newsletter" target="_blank" class="btn1">Learn More</a>
                    </div>
                </div>
                <div class="card1">
                    <img src="img/cupra/cupra-offer.jpg" alt="Offers & Promotions">
                    <div class="card-content1">
                    <span class="m-0 p-0  cupra-regular font20">Service Offers & Promotions</span>
                    <div class="cupra-light">
                        <p>Design a service plan that suits you.</p>
                        </div>
                        <a href="https://www.cupraofficial.sg/owners/service-offers-promotions/overview"  target="_blank" class="btn1">Learn More</a>
                        </div>
                </div>
                <div class="card1">
                    <img src="img/cupra/cupra-care.jpg" alt="CUPRA Care">
                    <div class="card-content1">
                        <span class="m-0 p-0 cupra-regular font20">CUPRA Care</span>
                        <div class="cupra-light">
                        <p>Premium service, expert maintenance, and exclusive benefits.</p>
                        </div>
                        <a href="https://www.cupraofficial.sg/owners/cupra-care/overview" target="_blank" class="btn1">Learn More</a>
                    </div>
                </div>
                <div class="card1">
                    <img src="img/cupra/SS-CI-Lifestyle.jpg" alt="CUPRA Assist">
                    <div class="card-content1">
                        <span class="m-0 p-0 cupra-regular font20">CUPRA Assist</span>
                        <div class="cupra-light">
                        <p>Drive with confidence and explore limitless possibilities knowing that we've got you covered.</p>
                        </div>
                        <a href="https://www.cupraofficial.sg/owners/cupra-assist/overview" target="_blank" class="btn1">Learn More</a>
                    </div>
                </div>
                <div class="card1">
                    <img src="img/cupra/cupra-warranty.jpg" alt="Warranty">
                    <div class="card-content1">
                        <span class="m-0 p-0 cupra-regular font20">Servicing & Warranty</span>
                        <div class="cupra-light">
                        <p>Comprehensive coverage for peace of mind.</p>
                        </div>
                        <a href="https://www.cupraofficial.sg/owners/servicing-and-warranty/warranty" target="_blank" class="btn1">Learn More</a>
                    </div>
                </div>
            </div>
        </div>
        <button class="slider-btn1 next1" onclick="moveSlide1(2)">&#10095;</button>

        <!-- Pagination for mobile -->
        <div class="rect-pagination d-md-none">
            <span class="rect active" onclick="goToSlide1(0)"></span>
            <span class="rect" onclick="goToSlide1(1)"></span>
            <span class="rect" onclick="goToSlide1(2)"></span>
        </div>
    </div>

  <script>
    let currentSlide1 = 0;
    let cardsPerView = window.innerWidth <= 768 ? 2 : 4; // 2 cards on mobile, 4 on desktop
    const totalCards = 5; // Total number of cards
    let maxSlides1 = Math.ceil(totalCards / cardsPerView) - 1;

    function updateSliderConfig() {
      cardsPerView = window.innerWidth <= 768 ? 2 : 4;
      if (window.innerWidth <= 768) {
        // Mobile: 3 slides (showing 2 cards each, total 5 cards: slides 0,1,2)
        maxSlides1 = Math.ceil(totalCards / cardsPerView) - 1;
      } else {
        // Desktop: 2 steps (step 0: cards 1-4, step 2: cards 3-5)
        maxSlides1 = 2;
      }

      // Reset to first slide if current slide is out of bounds
      if (currentSlide1 > maxSlides1) {
        currentSlide1 = maxSlides1;
      }
    }

    function updatePagination() {
      const paginationDots = document.querySelectorAll('.rect');
      paginationDots.forEach((dot, index) => {
        dot.classList.toggle('active', index === currentSlide1);
        // Show/hide dots based on maxSlides1
        dot.style.display = index <= maxSlides1 ? 'inline-block' : 'none';
      });
    }

    function moveSlide1(n) {
      const slider = document.querySelector('.slider-wrapper1');
      const prevBtn = document.querySelector('.prev1');
      const nextBtn = document.querySelector('.next1');

      if (!slider) return;

      // Calculate new slide position
      if (window.innerWidth <= 768) {
        // Mobile: move by 1 slide (showing 2 cards each)
        currentSlide1 += (n > 0 ? 1 : -1);
      } else {
        // Desktop: move by the specified amount (2 cards)
        currentSlide1 += n;
      }

      // Boundary checks
      if (currentSlide1 < 0) {
        currentSlide1 = 0;
      } else if (currentSlide1 > maxSlides1) {
        currentSlide1 = maxSlides1;
      }

      // Calculate transform percentage
      let transformPercent;
      if (window.innerWidth <= 768) {
        // Mobile: each slide shows 2 cards (50% each)
        transformPercent = -(currentSlide1 * 100);
      } else {
        // Desktop: each step moves by 1 card width (approximately 22%)
        transformPercent = -(currentSlide1 * 22);
      }

      slider.style.transform = `translateX(${transformPercent}%)`;

      // Update button visibility
      if (prevBtn) prevBtn.style.display = currentSlide1 === 0 ? 'none' : 'block';
      if (nextBtn) nextBtn.style.display = currentSlide1 >= maxSlides1 ? 'none' : 'block';

      // Update pagination
      updatePagination();
    }

    function goToSlide1(slideIndex) {
      currentSlide1 = slideIndex;
      if (currentSlide1 > maxSlides1) currentSlide1 = maxSlides1;
      if (currentSlide1 < 0) currentSlide1 = 0;

      const slider = document.querySelector('.slider-wrapper1');
      if (slider) {
        let transformPercent;
        if (window.innerWidth <= 768) {
          transformPercent = -(currentSlide1 * 100);
        } else {
          transformPercent = -(currentSlide1 * 22);
        }
        slider.style.transform = `translateX(${transformPercent}%)`;
      }

      updatePagination();
    }

    // Initialize slider on page load
    document.addEventListener('DOMContentLoaded', function() {
      updateSliderConfig();
      moveSlide1(0); // Initialize button states and pagination
    });

    // Handle window resize
    window.addEventListener('resize', function() {
      updateSliderConfig();
      moveSlide1(0); // Recalculate and reset to first slide
    });
  </script>


    
  <?php include 'footer.php' ?>
</body>
</html>

<?php include 'slider/modal.php' ?>
<?php include 'form/modal.php' ?>




