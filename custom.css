/* REMOVE DEFAULT SAPACING */
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}
html {
  scroll-behavior: smooth;
}
html.addOverflow,
body.addOverflow {
  overflow: hidden;
}
button:focus,
button:hover {
  outline: none;
}
a,
a:hover {
  color: black;
  text-decoration: none;
  /* transform: scale(1.1) !important; */
}
a,
a p:hover {
  color: black;
  text-decoration: none;
  /* transform: scale(1.1) !important; */
}

.footerSection-links a:hover {
  transform: scale(1.1) !important;
}

.footerSection-links a p:hover {
  transform: scale(1.1) !important;
}

@media screen and (max-width:768px){
  .footerSection-links a:hover {
    transform: none !important;
  }
  
  .footerSection-links a p:hover {
    transform: none !important;
  }
}

/* DEFAULT CONTAINER */
.normal-container {
  padding: 0 60px;
}
.mediumLeft-container {
  padding-left: 100px;
}
.medium-container {
  padding: 0 100px;
}
.removePaddingRight {
  padding-right: 0;
}
.removePaddingLeft {
  padding-left: 0;
}
.removePadding {
  padding-right: 0;
  padding-left: 0;
}

/* FONT SIZE AND FONT FAMILY*/
@font-face {
  font-family: "Vw Bold";
  src: url("../font/vw-text-bold.otf") format("opentype");
}
@font-face {
  font-family: "Vw Regular";
  src: url("../font/vw-text-regular.otf") format("opentype");
}
@font-face {
  font-family: "Vw Light";
  src: url("../font/vw-text-light.ttf") format("truetype");
}
.bold {
  font-family: "Vw Bold";
}
.regular {
  font-family: "Vw Regular";
}
.light {
  font-family: "Vw Light";
}
.font10 {
  font-size: 10px;
}
.font11 {
  font-size: 11px;
}
.font12 {
  font-size: 12px;
}
.font13 {
  font-size: 13px;
}
.font14 {
  font-size: 14px;
}
.font16 {
  font-size: 16px;
}
.font18 {
  font-size: 18px;
}
.font20 {
  font-size: 20px;
}
.font24 {
  font-size: 24px;
}
.font26 {
  font-size: 26px;
}
.font28 {
  font-size: 28px;
}
.font30 {
  font-size: 30px;
}
.font34 {
  font-size: 34px;
}
.font36 {
  font-size: 36px;
}
.font38 {
  font-size: 38px;
}
.font40 {
  font-size: 40px;
}
.font45 {
  font-size: 45px;
}
.font48 {
  font-size: 60px;
}
.font60 {
  font-size: 60px;
}
.lineHeight1 {
  line-height: 68px;
}
.lineHeight2 {
  line-height: 65px;
}
.displayNone {
  display: none !important;
}
.displayBlock {
  display: flex !important;
}

/* FLEX AND TEXT ALIGN*/
.justiftyCenter {
  justify-content: center;
}
.justiftyEnd {
  justify-content: flex-end;
}
.textCenter {
  text-align: center;
}
.textRight {
  text-align: right;
}
.sliderHeight {
  max-height: 200px;
  height: 200px;
}

/* NAV BAR */
/* .navBar {
  padding: 24px 0;
  position: absolute;
  top: 0;
  width: 100%;
  color: #ffff;
  z-index: 2;
  background-color: #1B1B1B !important;
  border-bottom: 1px solid #ffff;
  display: flex;
  justify-content: space-between;
  align-items: center;
} */

/* .navBar-firstCol div:nth-child(1) {
  margin-right: 70px;
}
.navBorderImg {
  width: 100%;
  margin-top: -8px;
  position: relative;
  z-index: -1;
}

.logoBar {
  height: 60px;
}
.logoRow {
  position: absolute;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  z-index: 9;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  height: 2px;
  top: 31px;
  right: 0;
  left: 0;
  margin-top: -8px;
}
.logoBar .logo-lineAfter,
.logoBar .logo-lineBefore {
  background-color: #001e50;
  height: 2px;
}

.logoLink {
  position: absolute;
  bottom: 0;
  right: 0;
  left: 0;
  top: 0;
}

.logo-container {
  position: relative;
  width: auto;
  box-sizing: border-box;
  height: 48px;
  padding: 0 12px;
}
.logo-lineBefore {
  width: 61.5%;
}
.logo-lineAfter {
  width: 37%;
}
.logo-VW svg {
  width: auto;
  height: 48px;
}
.logoBar svg path {
  fill: #001e50;
}

.logoBar.white svg path {
  fill: #fff;
}

.logoBar.white .logo-lineAfter,
.logoBar.white .logo-lineBefore {
  background-color: #fff;
} */
.navBar {
  padding: 24px 40px;
  position: absolute;
  top: 0;
  width: 100%;
  color: white;
  z-index: 2;
  background-color: #1B1B1B;
  border-bottom: 1px solid white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}


@media (max-width: 767px) {
  .navBar {
      padding: 13px 20px !important;
  }
}

.logo-container img {
  height: 30px;
}

.menu {
  display: flex;
  gap: 30px;
}

.menu a {
  text-decoration: none;
  color: white;
  font-size: 16px;
}

.menu a:not(:last-child) {
  border-right: 1px solid white;
  padding-right: 30px;
}

.menu a:hover {
  text-decoration: underline;
}

.discover-model-section .discover-model-items:hover .modal-name {
  color: black !important;
  cursor: default !important;
}

.hamburger {
  display: none;
  flex-direction: column;
  gap: 5px;
  cursor: pointer;
  z-index: 1100;
}
.hamburger div {
  width: 25px;
  height: 3px;
  background-color: white;
}
.sidenav1 {
  position: absolute;
  top: 50px;
  left: 50%;
  transform: translateX(-50%) scaleY(0);
  transform-origin: top;
  width: 100%;
  background-color: #222;
  padding: 30px;
  transition: transform 0.3s ease-in-out;
  z-index: 999;
  border-top: 1px solid white;
}
.sidenav1.active {
  transform: translateX(-50%) scaleY(1);
}

.sidenav1 a {
  padding: 20px 20px;
  display: block;
  color: white;
  text-decoration: none;
  font-size: 18px;
}
.sidenav1 a:hover {
  background-color: #444;
}
@media screen and (max-width: 768px) {
  .menu {
    display: none;
}.menu1 {
  display: block;
}
  .hamburger {
      display: flex;
  }
}
/* @media screen and (max-width: 480px) {
 
  .sidenav1 {
    top: 6% !important;
  }
} */

/* BANNER */
.bannerSection {
  background: url("../img/cupra/cupra-new-banner-d.jpg");
  background-repeat: no-repeat !important;
  background-size: cover !important;
  background-position: center !important;
  height: 100vh;
  max-height: 700px;


  /*position: relative;
	overflow: hidden;*/
}
@media (min-width: 1440px) {
  .bannerPosition {
      padding-top: 180px !important;
  }
}

.bannerPosition {
  /*	position: absolute;
	width: 100%;

	top: 38%;
	left: -5px;*/
  color: #fff;
  padding-top: 240px;
}
.bookTestDriveBtn {
  background: #78FAAE;
  color: #0E3A2F;
  border: 1px solid transparent;
  padding: 10px 23px 6px 23px;
  border-radius: 50px;
  transition: all .4s ease;
}
.exploreMoreBtn {
  margin-top: 80px;
}
#mouse {
  margin-right: 16px;
}
#mouse path {
  fill: #fff;
}

/* BROWSE CAR MODEL */
.browse-car-model {
  background-color: transparent;
  padding: 100px 0 50px 0;
}
.browseCarModel-tabLeft {
  min-height: 58px;
  max-height: 58px;
}
.browseCarModel-tabLeft div {
  margin-right: 70px;
  cursor: pointer;
}
.browseCarModel-tabLeft div {
  position: relative;
  overflow: hidden;
}
.browseCarModel-tabLeft div:hover {
  color: #00b0f0;
}
.browseCarModel-tabLeft div span::before {
  content: "";
  position: absolute;
  width: 0;
  height: 3px;
  bottom: -1px;
  opacity: 0;
  transition: all 0.3s ease;
}
.browseCarModel-tabLeft div:hover span::before {
  content: "";
  width: 100%;
  background-color: #00b0f0;
  opacity: 1;
}
.browseCarModel-tabLeft div.active {
  color: #00b0f0;
}
.browseCarModel-tabLeft div.active span::before {
  content: "";
  width: 100%;
  background-color: #00b0f0;
  opacity: 1;
}
.browseCarModel-tabLeft div.active:hover span::before {
  content: "";
  width: 100%;
  background-color: #00b0f0;
  opacity: 1;
}
.browseCarModel-tabRight {
  justify-content: flex-end;
  margin-right: 38px;
}
.browseCarModel-tabRight div {
  margin-right: 25px;
}
.default-col {
  margin-right: 18px;
  position: relative;
  margin-bottom: 70px;
}
.outerSlider-slick .slick-slide {
  max-width: 280px;
  width: 280px;
}
.outerSlider-box {
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;

  min-height: 380px;
  max-height: 380px;

  transition: all 0.3s ease;
  cursor: pointer;

  filter: grayscale(100%);
  position: relative;
  overflow: hidden;
}
.overlay-color {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  transition: all 0.3s ease;
}
.overlay-grey {
  background: rgba(178, 183, 188, 0.5);
}
.overlay-blue {
  background: rgba(17, 95, 179, 0.5);
}
.overlay-black {
  background: rgba(0, 0, 0, 0.5);
}
.overlay-white {
  background: rgba(255, 255, 255, 0.5);
}
.overlay-darkgrey {
  background: rgba(31, 32, 36, 0.5);
}
.carImagePosition {
  /*filter: grayscale(80%);*/
  transition: all 0.3s ease;
}
.carText {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;

  color: #fff;
  padding: 18px 23px;
}
.carText:hover button {
  opacity: 1;
  visibility: visible;
}

.outerSlider-box {
  background-color: transparent;
  color: #fff;
  /*padding: 18px 23px;*/
}
.innerSlider-box {
  background-color: transparent;
  color: #000;
  padding: 10px 0;
}
.outerSlider-boxBtn {
  margin-top: 26px;
  max-width: 234px;
  min-width: 234px;
}
.outerSlider-boxBtn button {
  width: 100%;
  background: #fff;
  color: #001e50;

  border: none;
  padding: 10px 0;
  border-radius: 50px;

  font-size: 14px;
  font-family: "Vw Regular";

  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}
/*.outerSlider-box:hover .overlay-grey
{
	background: rgba(108, 122, 137, 0);
}*/
/*.outerSlider-box:hover
{
	filter: grayscale(0);
}*/
.outerSlider-box:hover button {
  opacity: 1;
  visibility: visible;
}
.outerSlider-boxBtn button:nth-child(3) {
  margin-top: 10px;
}
.carImagePosition {
  position: absolute;
  width: 100%;
  top: 83%;
  right: 10px;
}
.individualSlider-box {
  background-color: #fff;
  color: #000;
  padding: 20px;
}
.addAnimation {
  animation-name: leftToRight;
  animation-duration: 1s;
}
@keyframes leftToRight {
  from {
    transform: translateX(30px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
.addFadeAnimation {
  animation-name: fadeInOut;
  animation-duration: 1s;
}
@keyframes fadeInOut {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.carousel-item {
  position: relative;
  overflow: hidden;
}
.carousel-item .col-md-5 div:nth-child(4) {
  margin-top: 65px;
}
.carousel-item .col-md-5 div:nth-child(5) {
  margin-top: 75px;
}
.innerArrow-positionLeft {
  position: absolute;
  width: auto;

  top: 55%;
  left: 25%;

  z-index: 20;
}
.innerArrow-positionRight {
  position: absolute;
  width: auto;

  top: 55%;
  left: 36%;

  z-index: 20;
}
.fullDetailBtn {
  background: #001e50;
  color: #fff;

  border: none;
  padding: 13px 80px;
  border-radius: 50px;
}
.innerArrow-positionLeft i,
.innerArrow-positionRight i {
  font-size: 40px;
  color: #001e50;
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: #fff;
}
.carousel-indicators {
  margin-bottom: 0;
  right: 405px;
  bottom: 178px;
}
.carousel-indicators li {
  border-radius: 50%;
  width: 10px;
  height: 10px;
  background-color: #95a8ae;

  margin-right: 16px;
}
.carousel-indicators li.active {
  background-color: #001a4c;
}

/* FEATURE */
.featureSection {
  background-color: transparent;
  padding: 40px 0 0 0;
}
.featureBox55 {
  width: 50%;
}
.featureBox45 {
  width: 50%;
}
.addBorder img {
  width: 100%;
  border-top: 10px solid #fff;
  border-right: 6px solid #fff;
  border-bottom: 10px solid #fff;
}
.secondFeatureMargin {
  margin-top: -60px;
}
/*#featureBoxRow01
{
	margin-top: -60px;
}*/
#featureBoxRow01 .mediumLeft-container div:nth-child(3) {
  margin-top: 33px;
  padding-right: 170px;
}
.findOutMoreBtn {
  background: #001e50;
  color: #fff;

  border: none;
  padding: 10px 60px;
  border-radius: 50px;

  margin-top: 36px;
}
#featureBoxRow02 .mediumLeft-container {
  padding-left: 80px;
}
/*#featureBoxRow02
{
	margin-top: -20px;
}*/
#featureBoxRow02 .mediumLeft-container div:nth-child(2) {
  margin-top: -13px;
}
#featureBoxRow02 .mediumLeft-container div:nth-child(3) {
  margin-top: 36px;
  padding-right: 150px;
}
.grid-container {
  display: grid;
  grid-template-areas: "signUp schedule";
  grid-gap: 10px;
  /*margin-top: -60px;*/
}
.item1,
.item2 {
  position: relative;
  overflow: hidden;
  z-index: -1;
}
.item1 {
  grid-area: signUp;
}
.item2 {
  grid-area: schedule;
}
.positionText {
  position: absolute;
  bottom: 50px;
  left: 60px;
  width: 100%;
  color: #fff;
}
.positionText div:nth-child(2) {
  margin-top: -8px;
}

/* YOU MIGHT INTERESTED IN */
.youMightSection {
  background: transparent;
  padding: 100px 0;
}
.medium-container .row:nth-child(1) .col-12 div:nth-child(2) {
  margin-top: -20px;
}
.grid-container1 {
  display: grid;
  grid-template-areas: "explore peace confidence";
  grid-gap: 46px;
}
.item3 {
  grid-area: explore;
}
.item4 {
  grid-area: peace;
}
.item5 {
  grid-area: confidence;
}

.youMightText div:nth-child(1) {
  margin-top: 33px;
}
.youMightText div:nth-child(3) {
  margin-top: -10px;
}

/* FOOTER */
.footerSection {
  background: transparent;
  padding-top: 5% !important;
  padding-bottom: 5% !important;
}

@media screen and (max-width:768px){
  .footerSection {
    padding-top: 10% !important;
    padding-bottom: 8% !important;
  }
  .footerSection p{
    font-size: 40px !important;
    line-height: 56px !important;
  }
}

.blackLine {
  border-bottom: 2px solid #001e50;
  margin-bottom: 80px;
}
.socialIcon {
  margin: 25px 0 88px 0;
}
.default-col:focus {
  outline: none;
  border: none;
}
.hideText {
  opacity: 1;
}

.inner-gridContainer {
  display: grid;
  grid-template-areas: "image info";
  grid-template-columns: 50% 50%;
  padding-right: 20px;
}
.inner-item1 {
  grid-area: image;
}
.inner-item2 {
  grid-area: info;
}
.inner-item1 {
  position: relative;

  background-repeat: no-repeat !important;
  background-size: cover !important;
  background-position: center !important;
}
.inner-item2 {
  background-color: #4cc8f4;
  color: #fff;

  padding: 60px 60px 60px 50px;
}
.inner-imgBg {
  position: relative;
}
.innerText-position {
  color: #fff;
  position: absolute;
  top: 6%;
  left: 8%;
}
.innerCarImg-position {
  width: 115%;
  position: absolute;
  bottom: -14%;
  left: -10.5%;
}
.innerSliderContainer-slick .default-col {
  margin-left: 100px;
}
@media (min-width: 576px) {
  .modal-dialog {
    max-width: 1100px;
  }
}

.padding15 {
  padding: 0 15px;
  margin-top: 68px;
}
.sidenav {
  height: auto;
  width: 100%;
  position: absolute;
  z-index: 1;
  top: 47px;
  left: 0;
  background-color: #FFF;
  overflow-x: hidden;
  transition: 0.5s;
  border-bottom: 1px solid #DDDDDD;

  transform: translateY(-100%);
  transition: all .4s ease;
  opacity: 0;
  visibility: hidden;
}
.sidenav.show
{
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}
.sidenav a {
  padding: 13px 23px;
  text-decoration: none;
  font-size: 16px;
  display: block;
  transition: 0.3s;

  font-family: "Vw Light";
}
.sidenav .topLink {
  padding: 8px 30px 16px 70px;
  text-decoration: none;
  font-size: 14px;
  font-family: "Vw Regular";
  margin-top: 46px;
}
.sidenav .topLink a {
  padding: 0;
  text-decoration: none;
  font-size: 14px;
  font-family: "Vw Light";
}
/*.sidenav a:hover {
  color: #f1f1f1;
}*/
.sidenav .d-flex {
  position: absolute;
  top: 26px;
  left: 26px;
}
@media screen and (max-height: 450px) {
  .sidenav {
    padding-top: 15px;
  }
  .sidenav a {
    font-size: 18px;
  }
}
.bar1 {
  width: 83%;
  background-color: #000;
  height: 3px;
  transform: scale(.8);
}
.bar2 {
  width: 83%;
  background-color: #000;
  height: 3px;
  transform: scale(.8);
  margin: 4px 0;
}
.bar3 {
  width: 83%;
  background-color: #000;
  height: 3px;
  transform: scale(.8);
}

/* THANK YOU PAGE */
.thankYouSection {
  background: transparent;
  position: relative;
  overflow: hidden;
}
.thankYou-textContainer {
  width: 100%;
  display: flex;
  justify-content: center;
}
.thankYou-text {
  padding: 130px 0 30px 0;
  /*max-width: 920px;*/

  display: flex;
}
.thankYou-text img {
  margin-bottom: 90px;
}
.thankYou-text div:nth-child(2) {
  margin-bottom: 30px;
}
.thankYou-text div:nth-child(3) {
  margin-bottom: 48px;
}
.mobileStatus,
.mobile-slick {
  display: none;
}
.firstDiv-adjust div:nth-child(1) span {
  opacity: 0;
  user-select: none;
}
.setDivWidth .font12 {
  width: 30%;
  margin-bottom: 28px;
}

.hide {
  display: none;
}

.click360Modal img {
  transition: all 0.3s ease;
}

.click360Modal img:hover {
  transform: scale(1.1);
  transition: all 0.3s ease;
}

.innerBtnLink,
.innerBtnLink button {
  width: 100%;
}

@media (min-width: 768px) {
  .innerBtnLink {
    width: 48%;
  }

  .innerBtnLink button {
    width: 100%;
  }
}

.thankYou-text div:nth-child(1) img {
  margin-right: 120px;
  width: 450px;
}
.footerNoteSection{
	margin-top: 50px;
	padding-top: 70px;
	padding-bottom: 40px;
	border-top: 1px solid #dfe4e8;
}
.justify-content-evenly{
	justify-content: space-evenly!important;
}


/* NEWEST CODE */
:root
{
  --primaryColor: #78FAAE;
  --darkgreen: #0E3A2F;
  --darkergreen: #00271d;
  --lightgrey: #A0A7A8;
  --lightergrey: #e2e2e2;
}
@font-face {
	font-family: "cupra bold";
	src: url("../font/cupra-bold.ttf") format('truetype');
}
@font-face {
	font-family: "cupra book";
	src: url("../font/cupra-book.ttf") format('truetype');
}
@font-face {
	font-family: "Cupra Regular";
	src: url("../font/cupra-regular.ttf") format('truetype');
}
@font-face {
	font-family: "Cupra Light";
	src: url("../font/cupra-light.ttf") format('truetype');
}
.cupra-bold
{
	font-family: "cupra bold";
}
.cupra-book
{
	font-family: "cupra book";
}
.cupra-regular
{
	font-family: "cupra regular";
}
.cupra-light
{
	font-family: "cupra light";
}

.discover-model-section .discover-model-title,
.explore-skoda-section .explore-skoda-title
{
    font-size: 30px;
}
.discover-model-section .discover-model-arrow
{
	cursor: pointer;
    top: 50%;
    transform: translate(0,-50%);

	background: #000;
  z-index: 1;
}
.discover-model-section .discover-model-arrow i
{
	font-size: 23px;
}
.discover-model-section .discover-model-arrow.left
{
	left: 4% !important;
}
.discover-model-section .discover-model-arrow.right
{
	right: 4% !important;
}
.discover-model-section .discover-model-items .modal-name
{
	color: black;
	font-size: 40px;
  transition: all .4s ease;
  line-height: 50px;
  font-family: "cupra light";
}

.discover-model-section .discover-model-items:hover .modal-name
{
  color: var(--primaryColor);
}
.discover-model-section .discover-model-items .modal-name
{
  max-width: 500px;
} 
.discover-model-items
{
  /* margin-right: 5% !important;
  margin-left: 5% !important;
  width: 35% !important; */
  /* width: 408px;
  margin-right: 30px; */
} 

.explore-skoda-section .explore-skoda-grid
{
  display: grid;
  gap: 23px;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
}
.explore-skoda-section .explore-skoda-grid .explore-skoda-items::after
{
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;

  width: 100%;
  height: 100%;
  background: linear-gradient(to top, rgba(0, 0, 0, .8), transparent, transparent);

  opacity: 0;
  visibility: hidden;
  transition: all .6s ease;
}
.explore-skoda-section .explore-skoda-grid .explore-skoda-items:hover .explore-skoda-items-img
{
  transform: scale(1.2);
}
.explore-skoda-section .explore-skoda-grid .explore-skoda-items:hover::after
{
  opacity: 1;
  visibility: visible;
}
.explore-skoda-section .explore-skoda-grid .explore-skoda-items .explore-skoda-items-text
{
  bottom: 23px;
  left: 33px;
  z-index: 1;
}
.explore-skoda-section .explore-skoda-grid .explore-skoda-items .explore-skoda-items-img
{
  object-fit: cover;
  transition: all .6s ease;
}
@media (max-width: 768px) {
  .bannerPosition .main-title {
      font-size: 36px !important;
      line-height: 46px !important;
  }

  .bannerPosition {
    color: #fff;
    padding-top: 460px !important;
}
.discover-model-section p{
  font-size: 40px !important;
  line-height: 56px !important;
}
.discover-model-items p{
  font-size: 20px !important;
  line-height: 36px !important;
}

  .btn2 {
    display: flex !important;
    margin: 20px !important;
    padding: 12px 25px !important;
    font-size: 12px !important;
}
.discover-model-section .discover-model-arrow.left
{
	left: 0% !important;
}
.discover-model-section .discover-model-arrow.right
{
	right: 0% !important;
}
}

@media (max-width:1024px)
{
  .bannerSection
  {
    max-height: 650px;
    background: url("../img/cupra/cupra-new-banner-m.jpg");
    background-position: 0 28% !important;
  }
  .bannerPosition .main-title
  {
    font-size: 30px;
    line-height: 43px;
  }
  .explore-skoda-section .explore-skoda-grid
  {
    gap: 13px;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(3, 1fr);
  }
  .explore-skoda-section .explore-skoda-grid .explore-skoda-items .explore-skoda-items-text p:first-child
  {
    font-size: 15px;
    line-height: 20px;
  }
  .explore-skoda-section .explore-skoda-grid .explore-skoda-items .explore-skoda-items-text p:last-child
  {
    font-size: 10px;
  }
  .explore-skoda-section .explore-skoda-grid .explore-skoda-items:nth-child(4)
  {
    grid-column-start: 1;
    grid-row-start: 3;
  }
  .explore-skoda-section .explore-skoda-grid .explore-skoda-items:last-child
  {
    grid-column-start: 2 !important;
    grid-row-start: 3 !important;
  }
  .normal-container
  {
    padding: 0 30px;
  }
  .explore-skoda-section .explore-skoda-grid .explore-skoda-items .explore-skoda-items-text
  {
    bottom: 13px;
    left: 13px;
  }
  .explore-skoda-section .explore-skoda-grid .explore-skoda-items .explore-skoda-items-img
  {
    max-height: 200px;
  }
  .explore-skoda-section .explore-skoda-grid .explore-skoda-items .explore-skoda-items-img.middle
  {
    max-height: 412px;
  }
}
.explore-skoda-section .explore-skoda-grid .explore-skoda-items:nth-child(2)
{
  grid-column-start: 1;
  grid-row-start: 2;
}
.explore-skoda-section .explore-skoda-grid .explore-skoda-items:nth-child(3)
{
  grid-column-start: 2;
  grid-row-end: span 2;
}
.explore-skoda-section .explore-skoda-grid .explore-skoda-items:last-child
{
  grid-column-start: 3;
  grid-row-start: 2;
}

.social-media-wrapper .social-media-items
{
  background: var(--primaryColor);
  border-radius: 50px;

  width: 50px;
  height: 50px;
  font-size: 18px;
  transition: all .4s ease;
}
.social-media-wrapper .social-media-items i
{
  color: var(--darkgreen);
}
.social-media-wrapper .social-media-items img
{
  filter: invert(16%) sepia(9%) saturate(3214%) hue-rotate(115deg) brightness(95%) contrast(92%);
}
.social-media-wrapper .social-media-items:hover
{
  background: var(--darkgreen);
}
.social-media-wrapper .social-media-items:hover i
{
  color: #FFF;
}
.social-media-wrapper .social-media-items:hover img
{
  filter: invert(100%) sepia(3%) saturate(10%) hue-rotate(56deg) brightness(102%) contrast(101%);
}

.footerSection-links
{
  border-top: 1px solid #D1D1D1;
  background: white;
  padding-top: 3% !important;
  padding-bottom: 3% !important;
}
.footerSection-links.copyright
{
  border-top: 1px solid #D1D1D1;
  background: white;
  padding: 1px !important;
  font-family: "cupra light";
}
.footerSection-links a:hover
{
  color: var(--primaryColor)
}
#footer_collapse i
{
  transform: rotate(0);
  transition: all .4s ease;
}
#footer_collapse.collapsed i
{
  transform: rotate(180deg);
}

.return-to-top
{
  width: max-content;
  right: 0;
  bottom: 0;
  cursor: pointer;

  background: black;
}

.bookTestDriveBtn:hover
{
  background: var(--darkergreen);
  color: var(--primaryColor);
  border: 1px solid var(--darkgreen);
}
.bookTestDriveBtn.whiteHover:hover
{
    background: transparent;
    border: 1px solid #FFF;
    color: #FFF;
}
.bookTestDriveBtn.exploreBtn
{
	background: transparent;
	color: #000;
	border: 1px solid #000;
	transition: all .4s ease;
}
.bookTestDriveBtn.exploreBtn:hover
{
	background: var(--lightergrey);
}
.primary-color
{
  color: var(--primaryColor);
}
.site-logo img
{
  /* max-width: 200px; */
  height: 30px !important;
}
.navBar a:hover div
{
  color: var(--darkgreen);
  font-family: "cupra book";
}

@media (max-width:520px)
{
  .discover-model-section .discover-model-title
  {
    line-height: 33px;
  }
  .discover-model-section .discover-model-arrow
  {
    position: relative !important;
    top: 0;
    transform: translate(0,0);
  }
  .discover-model-section .discover-model-arrow.left
  {
    left: 0;
  }
  .discover-model-section .discover-model-arrow.right
  {
    right: 0;
  }
  .discover-model-section .discover-model-title,
  .explore-skoda-section .explore-skoda-title,
  .footerSection .main-title
  {
    font-size: 28px;
  }
  .social-media-wrapper .social-media-items i
  {
    font-size: 18px;
  }
  .bannerSection
  {
    background-position: top !important;
    max-height: 650px;
  }
  .explore-skoda-section .explore-skoda-grid .explore-skoda-items .explore-skoda-items-text p:first-child
  {
    max-width: 125px;
  }
  .navBar::after
  {
    content: '';
    position: absolute;
    right: 60px;
    top: 0;

    width: 2px;
    height: 100%;
    background: #DDDDDD;
    display: none;
  }
}

.desktop-image,
.laptop-image,
.mobile-image {
    display: none;
}

/* Show desktop image for xl screens and above */
@media (min-width: 1920px) {
    .desktop-image {
        display: block;
    }
}

/* Show laptop image for lg screens only */
@media (min-width: 1025px) and (max-width: 1919px) {
    .laptop-image {
        display: block;
    }
}

/* Show mobile image for screens smaller than lg */
@media (max-width: 1024px) {
    .mobile-image {
        display: block;
    }
}

.apply-padding-top {
  /* padding-top: 1rem !important;  */
  padding-top: 0px !important;
}


.discover-model-section {
  padding-bottom: 5% !important;
}

.slider-container1 {
  position: relative;
  width: 100%;
  max-width: 100%;
  margin: auto;
  overflow: hidden;
  background-color: #111;
  padding: 5% !important;
}
.slider-wrapper1 {
  display: flex;
  transition: transform 0.5s ease-in-out;
  width: 150%;
  padding-top: 5%;
}
.slide1 {
  display: flex;
  gap: 35px;
  flex: 0 0 100%;
  justify-content: left;
  /* width: 100%; */
}
.card1 {
  /* background-color: #222; */
  flex: 0 0 calc(22% - 20px);
  /* border-radius: 10px; */
  overflow: hidden;
  text-align: left;
}

@media screen and (max-width:768px){

  .logo-container img {
    height: auto !important;
    width: 153px !important;
}

  .card1 {
    flex: 0 0 calc(50% - 5%);
  }
  .slider-wrapper1 {
    display: flex;
    overflow: hidden;
    width: 100%;
    transition: transform 0.5s ease-in-out;
}

.slide1 {
    display: flex;
    transition: transform 0.5s ease-in-out;
}

.card1 {
    scroll-snap-align: center;
    /* gap: 35px;
    width: 250%; Accommodate 5 cards at 50% each */
}
.slider-container1 {
  padding: 5% 0% 10% 5% !important;}

    .slider-container1 p {
      padding-top: 8% !important;
      padding-bottom: 3% !important;
    }
  
.slider-btn1 {
    display: none !important; /* Hide next/prev buttons */
}

/* Rectangle Pagination */
.rect-pagination {
    text-align: center;
    margin-top: 10px;
    display: flex;
    justify-content: center;
    gap: 5px;
}

.rect {
    height: 5px;
    width: 30px; /* Rectangle shape */
    background-color: #bbb;
    border-radius: 3px;
    display: inline-block;
    cursor: pointer;
    transition: background-color 0.3s ease, width 0.3s ease;
}

.rect.active {
    background-color: #717171;
    width: 40px; /* Slightly wider when active */
}
}
.card1 img {
  width: 100%;
  display: block;
}
.card-content1 {
  padding: 20px;
}
.card1 h3 {
  margin: 0 0 10px;
}
.card1 p {
  font-size: 14px;
  color: #ccc;
}
.card-content1 span {
  font-size: 20px;
  color: #ffff;
  padding-bottom: 15px !important;
}
.btn1 {
  display: inline-block;
  margin-top: 10px;
  padding: 12px 30px;
  background-color: #DBD3CB;
  color: #111;
  text-decoration: none;
  /* border-radius: 5px; */
  font-family: "cupra regular";
}
.btn1a {
  display: inline-block;
  /* margin-top: 10px; */
  padding: 10px 30px;
  background-color: #DBD3CB;
  color: #111;
  text-decoration: none;
  /* border-radius: 5px; */
  font-family: "cupra regular";
  font-size: 12px;
}

@media screen and (max-width:768px){
  /* .menu{
    display: none !important;
  } */
  .btn1a {
    display: inline-block;
    /* margin-top: 10px; */
    padding: 10px 30px;
    background-color: #DBD3CB;
    color: #111;
    text-decoration: none;
    /* border-radius: 5px; */
    font-family: "cupra regular";
    font-size: 12px;
  }
}
.btn2 {
  display: inline-block;
  margin-top: 10px;
  padding: 12px 30px;
  background-color: #000;
  color: #DBD3CB;
  text-decoration: none;
  /* border-radius: 5px; */
  font-family: "cupra regular";
}
.slider-btn1 {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(255, 255, 255);
  border: none;
  padding: 10px 18px;
  border-radius: 200px;
  cursor: pointer;
  z-index: 10;
}
.prev1 {
  left: 6%;
}
.next1 {
  right: 6%;
}
